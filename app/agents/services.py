import requests
import json
import time
from typing import Dict, Any, Optional, List
from django.conf import settings
from .models import Agent, AgentConnectionLog


class BaseAIService:
    """AI服务基类"""
    
    def __init__(self, agent: Agent):
        self.agent = agent
        self.api_key = agent.api_key
        self.api_endpoint = agent.api_endpoint
        self.config = agent.get_config()
    
    def send_message(self, message: str, conversation_history: List[Dict] = None) -> Dict[str, Any]:
        """发送消息到AI平台"""
        raise NotImplementedError("子类必须实现此方法")
    
    def test_connection(self) -> Dict[str, Any]:
        """测试连接"""
        try:
            # 对于需要conversation对象的服务（如RagFlow），创建一个临时的conversation对象
            # 这样可以避免在测试连接时出现session管理问题
            from conversations.models import Conversation

            # 创建一个临时的测试对话对象（不保存到数据库）
            test_conversation = Conversation(
                title="测试连接",
                agent=self.agent,
                user=self.agent.owner
            )

            response = self.send_message("Hello, this is a connection test.", conversation=test_conversation)
            return {
                'success': True,
                'response': response,
                'message': '连接成功'
            }
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'message': f'连接失败: {str(e)}'
            }
    
    def log_connection(self, success: bool, response_time: float, error_message: str = None):
        """记录连接日志"""
        status = 'success' if success else 'failed'
        AgentConnectionLog.objects.create(
            agent=self.agent,
            status=status,
            response_time=response_time,
            error_message=error_message or ''
        )


class DifyService(BaseAIService):
    """Dify平台服务"""

    def _build_api_url(self, endpoint):
        """根据endpoint构建正确的API URL"""
        endpoint = endpoint.rstrip('/')

        # 根据Dify官方文档，chatflow应用统一使用chat-messages端点
        # API key已经包含了应用信息，不需要在URL中指定app_id
        api_url = f"{endpoint}/v1/chat-messages"
        return api_url

    def send_message(self, message: str, conversation_history: List[Dict] = None, conversation=None) -> Dict[str, Any]:
        """发送消息到Dify"""
        if not self.api_endpoint or not self.api_key:
            raise ValueError("Dify API endpoint 和 API key 是必需的")

        # 构建API端点
        url = self._build_api_url(self.api_endpoint)

        print(f"Dify API URL: {url}")  # 调试信息

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        # 构建Chatflow API请求数据
        conversation_id = ""
        if conversation and conversation.external_session_id:
            conversation_id = conversation.external_session_id

        data = {
            'query': message,
            'response_mode': 'blocking',
            'user': f'user_{self.agent.owner.id}',
            'conversation_id': conversation_id,  # 空字符串表示新对话
            'inputs': {}  # 初始化inputs参数
        }

        # 添加配置参数到inputs中
        if self.config:
            # 将配置参数添加到inputs中，这是Dify推荐的方式
            for key, value in self.config.items():
                if key not in ['query', 'response_mode', 'user', 'conversation_id']:
                    data['inputs'][key] = value

        start_time = time.time()

        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response_time = time.time() - start_time

            if response.status_code == 200:
                result = response.json()
                self.log_connection(True, response_time)

                print(f"Dify API响应: {result}")  # 调试信息

                # 根据API类型处理不同的响应格式
                if 'workflows/run' in url:
                    # Workflow API响应格式
                    data = result.get('data', {})
                    outputs = data.get('outputs', {})

                    # workflow的输出可能在不同字段中
                    content = outputs.get('answer', '') or outputs.get('text', '') or str(outputs)

                    return {
                        'content': content,
                        'sources': [],  # workflow通常不返回sources
                        'metadata': {
                            'model': 'dify-workflow',
                            'workflow_id': data.get('workflow_id', ''),
                            'execution_id': data.get('id', ''),
                            'status': data.get('status', '')
                        }
                    }
                else:
                    # Chatflow API响应格式
                    # 保存会话ID到conversation对象
                    if conversation and result.get('conversation_id'):
                        conversation.external_session_id = result['conversation_id']
                        conversation.save(update_fields=['external_session_id'])
                        print(f"为对话 {conversation.id} 保存Dify conversation_id: {result['conversation_id']}")
                    else:
                        print(f"未获取到conversation_id或conversation对象为空: conversation={conversation}, conversation_id={result.get('conversation_id')}")

                    # 提取引用资源（如果有）
                    sources = []
                    metadata = result.get('metadata', {})
                    retriever_resources = metadata.get('retriever_resources', [])
                    if retriever_resources:
                        sources = [f"{res.get('dataset_name', '未知数据集')} - {res.get('document_name', '未知文档')}"
                                  for res in retriever_resources]

                    return {
                        'content': result.get('answer', ''),
                        'sources': sources,
                        'metadata': {
                            'model': metadata.get('usage', {}).get('model', 'dify'),
                            'tokens': metadata.get('usage', {}).get('total_tokens', 0),
                            'prompt_tokens': metadata.get('usage', {}).get('prompt_tokens', 0),
                            'completion_tokens': metadata.get('usage', {}).get('completion_tokens', 0),
                            'conversation_id': result.get('conversation_id', ''),
                            'message_id': result.get('message_id', '')
                        }
                    }
            else:
                error_msg = f"Dify API错误: {response.status_code} - {response.text}"
                self.log_connection(False, response_time, error_msg)
                raise Exception(error_msg)

        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            error_msg = f"Dify请求失败: {str(e)}"
            self.log_connection(False, response_time, error_msg)
            raise Exception(error_msg)

    def send_message_stream(self, message: str, conversation_history: List[Dict] = None, conversation=None):
        """发送消息到Dify并返回流式响应生成器"""
        if not self.api_endpoint or not self.api_key:
            raise ValueError("Dify API endpoint 和 API key 是必需的")

        # 构建API端点
        url = self._build_api_url(self.api_endpoint)

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        # 构建Chatflow API流式请求数据
        conversation_id = ""
        if conversation and conversation.external_session_id:
            conversation_id = conversation.external_session_id

        data = {
            'query': message,
            'response_mode': 'streaming',  # 启用流式响应
            'user': f'user_{self.agent.owner.id}',
            'conversation_id': conversation_id,  # 空字符串表示新对话
            'inputs': {}  # 初始化inputs参数
        }

        # 添加配置参数到inputs中
        if self.config:
            for key, value in self.config.items():
                if key not in ['query', 'response_mode', 'user', 'conversation_id']:
                    data['inputs'][key] = value

        start_time = time.time()

        try:
            # 使用stream=True来获取流式响应，增加超时时间以支持长时间对话
            response = requests.post(url, headers=headers, json=data, stream=True, timeout=300)

            if response.status_code == 200:
                response_time = time.time() - start_time
                self.log_connection(True, response_time)

                # 解析SSE流
                full_answer = ""
                conversation_id_from_stream = ""
                message_id_from_stream = ""
                last_processed_length = 0  # 记录上次处理的内容长度

                print(f"开始处理Dify SSE流，URL: {url}")

                for line in response.iter_lines(decode_unicode=True):
                    if not line:
                        continue

                    # 处理SSE格式的数据行
                    if line.startswith('data:'):
                        data_str = line[5:].strip()  # 移除'data:'前缀并去除空格

                        # 跳过空行和结束标记
                        if not data_str or data_str == '[DONE]':
                            continue

                        # 调试：记录原始数据
                        print(f"Dify SSE原始数据: {data_str[:200]}...")

                        try:
                            data_obj = json.loads(data_str)
                            event = data_obj.get('event', '')

                            print(f"Dify事件类型: {event}")

                            # 处理消息事件
                            if event == 'message':
                                # 消息内容 - Dify返回的是独立的内容片段，不是累积内容
                                answer = data_obj.get('answer', '')

                                # 详细调试信息
                                print(f"Dify消息片段: answer='{answer}' (长度: {len(answer)})")

                                # 保存会话信息
                                if data_obj.get('conversation_id'):
                                    conversation_id_from_stream = data_obj.get('conversation_id')
                                    # 立即保存会话ID，避免在流式响应中丢失
                                    if conversation and not conversation.external_session_id:
                                        conversation.external_session_id = conversation_id_from_stream
                                        conversation.save(update_fields=['external_session_id'])
                                        print(f"在message事件中为对话 {conversation.id} 保存Dify conversation_id: {conversation_id_from_stream}")
                                if data_obj.get('message_id'):
                                    message_id_from_stream = data_obj.get('message_id')

                                # 处理内容片段 - 每个answer都是独立的内容片段
                                if answer:
                                    # 直接将answer作为新增内容处理
                                    print(f"Dify新增内容: '{answer}' (长度: {len(answer)})")
                                    full_answer += answer  # 累积到完整答案中

                                    yield {
                                        'type': 'chunk',
                                        'content': answer,
                                        'full_content': full_answer
                                    }
                                else:
                                    print(f"Dify answer字段为空，跳过")

                            elif event == 'message_end':
                                print(f"Dify消息结束事件，完整内容长度: {len(full_answer)}")

                                # 消息结束，确保会话ID已保存
                                if conversation and conversation_id_from_stream:
                                    if not conversation.external_session_id or conversation.external_session_id != conversation_id_from_stream:
                                        conversation.external_session_id = conversation_id_from_stream
                                        conversation.save(update_fields=['external_session_id'])
                                        print(f"在message_end事件中为对话 {conversation.id} 保存Dify conversation_id: {conversation_id_from_stream}")
                                    else:
                                        print(f"对话 {conversation.id} 的Dify conversation_id已存在: {conversation.external_session_id}")

                                # 提取元数据和来源信息
                                metadata = data_obj.get('metadata', {})
                                retriever_resources = metadata.get('retriever_resources', [])
                                sources = []
                                if retriever_resources:
                                    sources = [f"{res.get('dataset_name', '未知数据集')} - {res.get('document_name', '未知文档')}"
                                              for res in retriever_resources]

                                yield {
                                    'type': 'complete',
                                    'content': full_answer,  # 返回完整的累积内容
                                    'sources': sources,
                                    'metadata': {
                                        'model': metadata.get('usage', {}).get('model', 'dify'),
                                        'tokens': metadata.get('usage', {}).get('total_tokens', 0),
                                        'conversation_id': conversation_id_from_stream,
                                        'message_id': message_id_from_stream
                                    }
                                }
                                break

                            # 处理其他可能的事件类型（如workflow相关事件）
                            elif event in ['workflow_started', 'node_started', 'node_finished', 'workflow_finished']:
                                # 记录但不处理这些事件
                                print(f"Dify工作流事件: {event}")
                                continue

                            # 处理错误事件
                            elif event == 'error':
                                error_msg = data_obj.get('message', '未知错误')
                                print(f"Dify返回错误事件: {error_msg}")
                                yield {
                                    'type': 'error',
                                    'message': f'Dify处理错误: {error_msg}'
                                }
                                break

                            # 处理未知事件类型
                            else:
                                print(f"Dify未知事件类型: {event}, 数据: {data_obj}")
                                continue

                        except json.JSONDecodeError as e:
                            # 改进JSON解析错误处理
                            print(f"Dify SSE JSON解析错误: {e}")
                            print(f"原始数据: {data_str}")

                            # 尝试处理可能的部分JSON数据
                            if data_str.strip():
                                # 如果数据不为空，尝试作为纯文本处理
                                yield {
                                    'type': 'chunk',
                                    'content': data_str,
                                    'full_content': full_answer + data_str
                                }
                                full_answer += data_str
                            continue

                print(f"Dify SSE流处理完成，最终内容长度: {len(full_answer)}")

            else:
                error_msg = f"Dify API错误: {response.status_code} - {response.text}"
                response_time = time.time() - start_time
                self.log_connection(False, response_time, error_msg)
                raise Exception(error_msg)

        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            error_msg = f"Dify流式请求失败: {str(e)}"
            self.log_connection(False, response_time, error_msg)
            raise Exception(error_msg)


class RagFlowService(BaseAIService):
    """RagFlow平台服务"""

    def send_message(self, message: str, conversation_history: List[Dict] = None, conversation=None) -> Dict[str, Any]:
        """发送消息到RagFlow"""
        if not self.api_endpoint or not self.api_key:
            raise ValueError("RagFlow API endpoint 和 API key 是必需的")

        # 从配置中获取agent_id，或使用platform_agent_id
        agent_id = self.config.get('agent_id') or self.agent.platform_agent_id

        if not agent_id:
            raise ValueError("RagFlow agent_id 是必需的")

        # 获取或创建session（传入conversation对象以便管理session_id）
        session_id = self._get_or_create_session(agent_id, conversation)

        # 使用RagFlow的agent completions API
        url = f"{self.api_endpoint.rstrip('/')}/api/v1/agents/{agent_id}/completions"

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        # 构建请求数据 - 禁用流式响应以获取完整JSON响应
        data = {
            'question': message,
            'stream': False,  # 禁用流式响应
            'session_id': session_id
        }

        start_time = time.time()

        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response_time = time.time() - start_time

            print(f"RagFlow API响应状态: {response.status_code}")
            print(f"RagFlow API响应内容: {response.text[:500]}...")  # 调试信息

            if response.status_code == 200:
                try:
                    result = response.json()
                    self.log_connection(True, response_time)

                    # 解析RagFlow格式的响应
                    if result.get('code') == 0 and result.get('data'):
                        data = result['data']
                        content = data.get('answer', '')

                        # 提取引用信息（如果有）
                        sources = []
                        reference = data.get('reference', {})
                        if reference:
                            # RagFlow的引用信息在reference字段中
                            sources = list(reference.keys()) if isinstance(reference, dict) else []

                        return {
                            'content': content,
                            'sources': sources,
                            'metadata': {
                                'model': 'ragflow',
                                'session_id': data.get('session_id', ''),
                                'message_id': data.get('id', '')
                            }
                        }
                    else:
                        error_msg = result.get('message', '未知错误')
                        return {
                            'content': f'RagFlow返回错误: {error_msg}',
                            'sources': [],
                            'metadata': {}
                        }
                except json.JSONDecodeError as e:
                    error_msg = f"RagFlow返回无效JSON: {str(e)}, 响应内容: {response.text[:200]}"
                    self.log_connection(False, response_time, error_msg)
                    raise Exception(error_msg)
            else:
                error_msg = f"RagFlow API错误: {response.status_code} - {response.text}"
                self.log_connection(False, response_time, error_msg)
                raise Exception(error_msg)

        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            error_msg = f"RagFlow请求失败: {str(e)}"
            self.log_connection(False, response_time, error_msg)
            raise Exception(error_msg)

    def _get_or_create_session(self, agent_id: str, conversation=None) -> str:
        """获取或创建RagFlow session"""
        # 如果传入了conversation对象，检查是否已有session_id
        if conversation and conversation.external_session_id:
            print(f"使用已存在的RagFlow session: {conversation.external_session_id}")
            return conversation.external_session_id

        # 创建新的session
        session_id = self._create_session(agent_id)

        # 如果传入了conversation对象，保存session_id
        if conversation and session_id:
            conversation.external_session_id = session_id
            conversation.save(update_fields=['external_session_id'])
            print(f"为对话 {conversation.id} 保存新的RagFlow session: {session_id}")

        return session_id

    def _create_session(self, agent_id: str) -> str:
        """创建新的RagFlow session"""
        url = f"{self.api_endpoint.rstrip('/')}/api/v1/agents/{agent_id}/sessions"

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        # 创建session的请求数据（根据agent的Begin组件参数）
        data = {}

        try:
            response = requests.post(url, headers=headers, json=data, timeout=10)

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0 and result.get('data', {}).get('id'):
                    session_id = result['data']['id']
                    print(f"RagFlow session创建成功: {session_id}")
                    return session_id
                else:
                    raise Exception(f"创建session失败: {result.get('message', '未知错误')}")
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text}")

        except Exception as e:
            print(f"创建RagFlow session失败: {str(e)}")
            # 返回一个默认的session_id，让后续请求尝试
            return "default_session"

    def send_message_stream(self, message: str, conversation_history: List[Dict] = None, conversation=None):
        """发送消息到RagFlow并返回流式响应生成器"""
        if not self.api_endpoint or not self.api_key:
            raise ValueError("RagFlow API endpoint 和 API key 是必需的")

        # 从配置中获取agent_id，或使用platform_agent_id
        agent_id = self.config.get('agent_id') or self.agent.platform_agent_id

        if not agent_id:
            raise ValueError("RagFlow agent_id 是必需的")

        # 获取或创建session（传入conversation对象以便管理session_id）
        session_id = self._get_or_create_session(agent_id, conversation)

        # 使用RagFlow的agent completions API
        url = f"{self.api_endpoint.rstrip('/')}/api/v1/agents/{agent_id}/completions"

        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

        # 构建请求数据 - 启用流式响应
        data = {
            'question': message,
            'stream': True,  # 启用流式响应
            'session_id': session_id
        }

        start_time = time.time()

        try:
            # 使用stream=True来获取流式响应，增加超时时间以支持长时间对话
            response = requests.post(url, headers=headers, json=data, stream=True, timeout=300)

            if response.status_code == 200:
                response_time = time.time() - start_time
                self.log_connection(True, response_time)

                # 解析SSE流
                last_answer = ""
                sources = []

                for line in response.iter_lines(decode_unicode=True):
                    if not line:
                        continue

                    # 处理SSE格式的数据行
                    if line.startswith('data:'):
                        data_str = line[5:].strip()  # 移除'data:'前缀并去除空格

                        # 跳过空行
                        if not data_str:
                            continue

                        # 调试：打印原始响应数据
                        print(f"RagFlow SSE原始数据: {data_str}")

                        try:
                            data_obj = json.loads(data_str)

                            # 检查响应状态
                            if data_obj.get('code') == 0:
                                data_content = data_obj.get('data')

                                # 处理字典类型的数据内容
                                if isinstance(data_content, dict):
                                    answer = data_content.get('answer', '')
                                    session_id_from_response = data_content.get('session_id', '')

                                    # 检查是否是状态消息（如 'xxx is running...'）
                                    if answer and ('is running' in answer or 'running...' in answer):
                                        # 发送状态消息，前端应该显示并准备被覆盖
                                        yield {
                                            'type': 'status',
                                            'content': answer,
                                            'session_id': session_id_from_response,
                                            'message_id': data_content.get('id', '')
                                        }
                                        continue

                                    # 处理有内容的正式回复
                                    if answer and answer != last_answer:
                                        # 计算新增内容
                                        if len(answer) > len(last_answer):
                                            new_content = answer[len(last_answer):]
                                            if new_content:
                                                yield {
                                                    'type': 'chunk',
                                                    'content': new_content,
                                                    'full_content': answer,
                                                    'session_id': session_id_from_response,
                                                    'message_id': data_content.get('id', ''),
                                                    'reference': data_content.get('reference', {})
                                                }
                                                last_answer = answer

                                    # 提取参考来源信息
                                    reference = data_content.get('reference', {})
                                    if reference and isinstance(reference, dict):
                                        chunks = reference.get('chunks', [])
                                        if chunks:
                                            sources = [chunk.get('content_with_weight', '') for chunk in chunks if chunk.get('content_with_weight')]

                                # 处理流结束标志
                                elif data_content is True or data_content == "":
                                    # 流结束，发送完成信号（包含完整内容）
                                    yield {
                                        'type': 'complete',
                                        'content': last_answer,  # 返回完整的累积内容
                                        'sources': sources,
                                        'session_id': session_id
                                    }
                                    break

                            # 处理错误响应
                            elif data_obj.get('code') != 0:
                                error_msg = data_obj.get('message', '未知错误')
                                print(f"RagFlow API错误: {error_msg}")
                                continue

                        except json.JSONDecodeError as e:
                            # 记录解析错误但继续处理
                            print(f"RagFlow SSE解析错误: {e}, 数据: {data_str[:100]}")
                            continue
            else:
                error_msg = f"RagFlow API错误: {response.status_code} - {response.text}"
                response_time = time.time() - start_time
                self.log_connection(False, response_time, error_msg)
                raise Exception(error_msg)

        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            error_msg = f"RagFlow请求失败: {str(e)}"
            self.log_connection(False, response_time, error_msg)
            raise Exception(error_msg)


class OpenAIService(BaseAIService):
    """OpenAI平台服务"""
    
    def send_message(self, message: str, conversation_history: List[Dict] = None, conversation=None) -> Dict[str, Any]:
        """发送消息到OpenAI"""
        if not self.api_key:
            raise ValueError("OpenAI API key 是必需的")
        
        # 使用自定义endpoint或默认OpenAI endpoint
        base_url = self.api_endpoint or "https://api.openai.com/v1"
        url = f"{base_url.rstrip('/')}/chat/completions"
        
        headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }
        
        # 构建消息历史
        messages = []
        if conversation_history:
            messages.extend(conversation_history)
        messages.append({'role': 'user', 'content': message})
        
        # 构建请求数据
        data = {
            'model': self.config.get('model', 'gpt-3.5-turbo'),
            'messages': messages,
            'max_tokens': self.config.get('max_tokens', 1000),
            'temperature': self.config.get('temperature', 0.7)
        }
        
        start_time = time.time()
        
        try:
            response = requests.post(url, headers=headers, json=data, timeout=30)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                self.log_connection(True, response_time)
                
                choice = result.get('choices', [{}])[0]
                usage = result.get('usage', {})
                
                return {
                    'content': choice.get('message', {}).get('content', ''),
                    'metadata': {
                        'model': result.get('model', ''),
                        'tokens': usage.get('total_tokens', 0),
                        'prompt_tokens': usage.get('prompt_tokens', 0),
                        'completion_tokens': usage.get('completion_tokens', 0)
                    }
                }
            else:
                error_msg = f"OpenAI API错误: {response.status_code} - {response.text}"
                self.log_connection(False, response_time, error_msg)
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            error_msg = f"OpenAI请求失败: {str(e)}"
            self.log_connection(False, response_time, error_msg)
            raise Exception(error_msg)


class CustomService(BaseAIService):
    """自定义平台服务"""
    
    def send_message(self, message: str, conversation_history: List[Dict] = None, conversation=None) -> Dict[str, Any]:
        """发送消息到自定义平台"""
        if not self.api_endpoint:
            raise ValueError("自定义平台需要API endpoint")
        
        headers = {
            'Content-Type': 'application/json'
        }
        
        if self.api_key:
            headers['Authorization'] = f'Bearer {self.api_key}'
        
        # 构建请求数据
        data = {
            'message': message,
            'history': conversation_history or []
        }
        
        # 添加配置参数
        if self.config:
            data.update(self.config)
        
        start_time = time.time()
        
        try:
            response = requests.post(self.api_endpoint, headers=headers, json=data, timeout=30)
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                result = response.json()
                self.log_connection(True, response_time)
                
                return {
                    'content': result.get('response', result.get('content', '')),
                    'metadata': result.get('metadata', {})
                }
            else:
                error_msg = f"自定义API错误: {response.status_code} - {response.text}"
                self.log_connection(False, response_time, error_msg)
                raise Exception(error_msg)
                
        except requests.exceptions.RequestException as e:
            response_time = time.time() - start_time
            error_msg = f"自定义API请求失败: {str(e)}"
            self.log_connection(False, response_time, error_msg)
            raise Exception(error_msg)


class AIServiceFactory:
    """AI服务工厂类"""
    
    @staticmethod
    def create_service(agent: Agent) -> BaseAIService:
        """根据智能体平台创建对应的服务"""
        service_map = {
            'dify': DifyService,
            'ragflow': RagFlowService,
            'openai': OpenAIService,
            'custom': CustomService
        }
        
        service_class = service_map.get(agent.platform)
        if not service_class:
            raise ValueError(f"不支持的平台类型: {agent.platform}")
        
        return service_class(agent)
