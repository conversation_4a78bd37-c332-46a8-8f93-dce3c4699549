from rest_framework import viewsets, status, permissions, filters
from rest_framework.decorators import action, api_view, permission_classes
from rest_framework.response import Response
from rest_framework.views import APIView
from django_filters.rest_framework import DjangoFilterBackend
from django.shortcuts import get_object_or_404
from django.db import models
from django.http import HttpResponse
import json
import uuid
from datetime import datetime, timedelta

from .models import Conversation, Message, ConversationShare
from .serializers import (
    ConversationSerializer,
    ConversationListSerializer,
    ConversationCreateSerializer,
    MessageSerializer,
    MessageCreateSerializer,
    SendMessageSerializer,
    ConversationShareSerializer,
    ConversationStatsSerializer,
    ConversationExportSerializer
)
from agents.models import Agent
from core.models import SystemSettings


class ConversationViewSet(viewsets.ModelViewSet):
    """对话视图集"""

    permission_classes = [permissions.AllowAny]  # 暂时允许匿名用户
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['status', 'agent__platform']
    search_fields = ['title']
    ordering_fields = ['created_at', 'updated_at', 'last_message_at']
    ordering = ['-updated_at']

    def get_queryset(self):
        """获取对话"""
        # 如果用户已认证，返回用户的对话
        if self.request.user.is_authenticated:
            return Conversation.objects.filter(user=self.request.user)
        else:
            # 对于匿名用户，返回anonymous用户的对话
            from django.contrib.auth import get_user_model
            User = get_user_model()
            try:
                anonymous_user = User.objects.get(username='anonymous')
                return Conversation.objects.filter(user=anonymous_user)
            except User.DoesNotExist:
                return Conversation.objects.none()

    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return ConversationCreateSerializer
        elif self.action == 'list':
            return ConversationListSerializer
        return ConversationSerializer

    def perform_create(self, serializer):
        """创建对话时设置用户并检查会话数量限制"""
        # 如果用户已认证，使用当前用户；否则创建默认用户
        if self.request.user.is_authenticated:
            user = self.request.user
        else:
            # 获取或创建默认用户
            from django.contrib.auth import get_user_model
            User = get_user_model()
            user, created = User.objects.get_or_create(
                username='anonymous',
                defaults={'email': '<EMAIL>'}
            )

        # 检查会话数量限制
        self._enforce_conversation_limit(user)

        serializer.save(user=user)

    def _enforce_conversation_limit(self, user):
        """强制执行会话数量限制"""
        # 获取系统设置
        settings = SystemSettings.get_settings()
        max_conversations = settings.max_conversations_per_user

        # 获取用户当前有用户消息的会话数量
        conversations_with_user_messages = Conversation.objects.filter(
            user=user,
            messages__type='user'
        ).distinct()
        current_count = conversations_with_user_messages.count()

        if current_count >= max_conversations:
            # 删除最旧的有用户消息的会话，直到数量符合限制
            conversations_to_delete = current_count - max_conversations + 1

            oldest_conversations = conversations_with_user_messages.order_by('updated_at')[:conversations_to_delete]

            for conversation in oldest_conversations:
                conversation.delete()
                print(f"删除用户 {user.username} 的旧会话: {conversation.title}")

        # 同时清理没有用户消息的空会话
        empty_conversations = Conversation.objects.filter(
            user=user
        ).exclude(
            messages__type='user'
        ).distinct()

        if empty_conversations.exists():
            empty_count = empty_conversations.count()
            empty_conversations.delete()
            print(f"清理用户 {user.username} 的 {empty_count} 个空会话")

        print(f"用户 {user.username} 当前有效会话数: {current_count}, 限制: {max_conversations}")

    @action(detail=True, methods=['get'])
    def messages(self, request, pk=None):
        """获取对话的消息列表"""
        conversation = self.get_object()
        messages = conversation.messages.order_by('created_at')

        # 分页
        page = self.paginate_queryset(messages)
        if page is not None:
            serializer = MessageSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        serializer = MessageSerializer(messages, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def send_message(self, request, pk=None):
        """发送消息到对话"""
        conversation = self.get_object()

        # 创建用户消息
        message_data = {
            'conversation': conversation.id,
            'type': 'user',
            'content': request.data.get('content', ''),
            'metadata': request.data.get('metadata', {})
        }

        serializer = MessageCreateSerializer(data=message_data)
        if serializer.is_valid():
            user_message = serializer.save(conversation=conversation)

            # 这里应该调用AI平台API获取回复
            # 暂时返回模拟回复
            assistant_content = self._generate_assistant_response(
                conversation, user_message.content
            )

            # 创建助手回复
            assistant_message = Message.objects.create(
                conversation=conversation,
                type='assistant',
                content=assistant_content,
                metadata={'model': conversation.agent.platform}
            )

            # 更新智能体使用统计
            conversation.agent.increment_usage()

            return Response({
                'user_message': MessageSerializer(user_message).data,
                'assistant_message': MessageSerializer(assistant_message).data
            })

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    def _generate_assistant_response(self, conversation, user_content):
        """生成助手回复"""
        from agents.services import AIServiceFactory

        try:
            service = AIServiceFactory.create_service(conversation.agent)

            # 获取对话历史（最近10条消息）
            recent_messages = conversation.messages.order_by('-created_at')[:10]
            conversation_history = []

            for msg in reversed(recent_messages):
                role = 'user' if msg.type == 'user' else 'assistant'
                conversation_history.append({
                    'role': role,
                    'content': msg.content
                })

            # 调用AI服务
            result = service.send_message(user_content, conversation_history)
            return result.get('content', f"{conversation.agent.name}回复：{user_content}")

        except Exception as e:
            print(f"AI服务调用失败: {str(e)}")
            # 返回错误提示
            return f"抱歉，{conversation.agent.name}暂时无法回复，请稍后重试。错误信息：{str(e)}"

    @action(detail=True, methods=['post'])
    def share(self, request, pk=None):
        """分享对话"""
        conversation = self.get_object()

        # 检查是否已经有分享记录
        share, created = ConversationShare.objects.get_or_create(
            conversation=conversation,
            defaults={
                'is_public': request.data.get('is_public', False),
                'password': request.data.get('password', ''),
                'expires_at': None  # 可以根据需要设置过期时间
            }
        )

        if not created:
            # 更新现有分享设置
            share.is_public = request.data.get('is_public', share.is_public)
            share.password = request.data.get('password', share.password)
            share.save()

        serializer = ConversationShareSerializer(share, context={'request': request})
        return Response(serializer.data)

    @action(detail=True, methods=['get'])
    def export(self, request, pk=None):
        """导出对话"""
        conversation = self.get_object()
        serializer = ConversationExportSerializer(data=request.query_params)

        if serializer.is_valid():
            format_type = serializer.validated_data['format']
            include_metadata = serializer.validated_data['include_metadata']
            include_sources = serializer.validated_data['include_sources']

            if format_type == 'json':
                return self._export_json(conversation, include_metadata, include_sources)
            elif format_type == 'txt':
                return self._export_txt(conversation)
            elif format_type == 'md':
                return self._export_markdown(conversation, include_sources)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def clear_messages(self, request, pk=None):
        """清空对话的所有消息"""
        conversation = self.get_object()

        # 删除所有消息
        deleted_count = conversation.messages.count()
        conversation.messages.all().delete()

        # 重置对话统计信息
        conversation.message_count = 0
        conversation.last_message_at = None
        conversation.save(update_fields=['message_count', 'last_message_at'])

        return Response({
            'success': True,
            'message': f'已清空 {deleted_count} 条消息',
            'deleted_count': deleted_count
        }, status=status.HTTP_200_OK)

    def _export_json(self, conversation, include_metadata, include_sources):
        """导出为JSON格式"""
        messages = conversation.messages.order_by('created_at')
        data = {
            'conversation': {
                'id': str(conversation.id),
                'title': conversation.title,
                'agent': conversation.agent.name,
                'created_at': conversation.created_at.isoformat(),
                'message_count': conversation.message_count
            },
            'messages': []
        }

        for message in messages:
            message_data = {
                'type': message.type,
                'content': message.content,
                'created_at': message.created_at.isoformat()
            }

            if include_metadata and message.metadata:
                message_data['metadata'] = message.metadata

            if include_sources and message.sources:
                message_data['sources'] = message.sources

            data['messages'].append(message_data)

        response = HttpResponse(
            json.dumps(data, ensure_ascii=False, indent=2),
            content_type='application/json'
        )
        response['Content-Disposition'] = f'attachment; filename="{conversation.title}.json"'
        return response

    def _export_txt(self, conversation):
        """导出为文本格式"""
        messages = conversation.messages.order_by('created_at')
        content = f"对话标题: {conversation.title}\n"
        content += f"智能体: {conversation.agent.name}\n"
        content += f"创建时间: {conversation.created_at.strftime('%Y-%m-%d %H:%M:%S')}\n"
        content += "=" * 50 + "\n\n"

        for message in messages:
            sender = "用户" if message.type == 'user' else conversation.agent.name
            content += f"[{message.created_at.strftime('%H:%M:%S')}] {sender}:\n"
            content += f"{message.content}\n\n"

        response = HttpResponse(content, content_type='text/plain; charset=utf-8')
        response['Content-Disposition'] = f'attachment; filename="{conversation.title}.txt"'
        return response

    def _export_markdown(self, conversation, include_sources):
        """导出为Markdown格式"""
        messages = conversation.messages.order_by('created_at')
        content = f"# {conversation.title}\n\n"
        content += f"**智能体**: {conversation.agent.name}  \n"
        content += f"**创建时间**: {conversation.created_at.strftime('%Y-%m-%d %H:%M:%S')}  \n"
        content += f"**消息数量**: {conversation.message_count}\n\n"
        content += "---\n\n"

        for message in messages:
            if message.type == 'user':
                content += f"## 👤 用户\n\n{message.content}\n\n"
            else:
                content += f"## 🤖 {conversation.agent.name}\n\n{message.content}\n\n"

                if include_sources and message.sources:
                    content += "**参考来源**:\n"
                    for source in message.sources:
                        content += f"- {source}\n"
                    content += "\n"

            content += f"*{message.created_at.strftime('%Y-%m-%d %H:%M:%S')}*\n\n---\n\n"

        response = HttpResponse(content, content_type='text/markdown; charset=utf-8')
        response['Content-Disposition'] = f'attachment; filename="{conversation.title}.md"'
        return response


class MessageViewSet(viewsets.ReadOnlyModelViewSet):
    """消息视图集（只读）"""

    serializer_class = MessageSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['type', 'status']
    search_fields = ['content']
    ordering_fields = ['created_at']
    ordering = ['created_at']

    def get_queryset(self):
        """获取用户对话的消息"""
        return Message.objects.filter(conversation__user=self.request.user)


@api_view(['POST'])
@permission_classes([permissions.AllowAny])  # 暂时允许匿名用户
def send_message_view(request):
    """发送消息API"""
    serializer = SendMessageSerializer(data=request.data, context={'request': request})

    if serializer.is_valid():
        conversation_id = serializer.validated_data['conversation_id']
        content = serializer.validated_data['content']
        message_type = serializer.validated_data['type']
        metadata = serializer.validated_data['metadata']

        # 获取对话，暂时不检查用户权限
        conversation = get_object_or_404(Conversation, id=conversation_id)

        # 创建消息
        message = Message.objects.create(
            conversation=conversation,
            type=message_type,
            content=content,
            metadata=metadata
        )

        response_data = {
            'message': MessageSerializer(message).data
        }

        # 如果是用户消息，生成AI回复
        if message_type == 'user':
            try:
                # 调用AI平台API
                from agents.services import AIServiceFactory

                # 创建AI服务
                service = AIServiceFactory.create_service(conversation.agent)

                # 获取对话历史
                recent_messages = Message.objects.filter(
                    conversation=conversation
                ).order_by('-created_at')[:10]

                conversation_history = []
                for msg in reversed(recent_messages):
                    if msg.type in ['user', 'assistant']:
                        role = 'user' if msg.type == 'user' else 'assistant'
                        conversation_history.append({
                            'role': role,
                            'content': msg.content
                        })

                # 调用AI服务（传递conversation对象以便管理session）
                result = service.send_message(content, conversation_history, conversation)
                assistant_content = result.get('content', f"抱歉，{conversation.agent.name}暂时无法回复。")

                # 提取来源信息
                sources = result.get('sources', [])
                metadata = {'model': conversation.agent.platform}
                if sources:
                    metadata['sources'] = sources

            except Exception as e:
                print(f"AI服务调用失败: {str(e)}")
                assistant_content = f"抱歉，{conversation.agent.name}暂时无法回复，请稍后重试。"
                metadata = {'model': conversation.agent.platform, 'error': str(e)}

            assistant_message = Message.objects.create(
                conversation=conversation,
                type='assistant',
                content=assistant_content,
                metadata=metadata
            )

            response_data['assistant_message'] = MessageSerializer(assistant_message).data

            # 更新智能体使用统计
            conversation.agent.increment_usage()

        return Response(response_data)

    return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@api_view(['GET'])
@permission_classes([permissions.IsAuthenticated])
def conversation_stats_view(request):
    """对话统计API"""
    user = request.user

    # 基础统计
    total_conversations = Conversation.objects.filter(user=user).count()
    active_conversations = Conversation.objects.filter(user=user, status='active').count()
    total_messages = Message.objects.filter(conversation__user=user).count()

    avg_messages = total_messages / total_conversations if total_conversations > 0 else 0

    # 最常用的智能体
    most_used_agents = list(
        Conversation.objects.filter(user=user)
        .values('agent__name', 'agent__avatar')
        .annotate(count=models.Count('id'))
        .order_by('-count')[:5]
    )

    # 最近活动
    recent_conversations = Conversation.objects.filter(user=user).order_by('-updated_at')[:10]
    recent_activity = ConversationListSerializer(recent_conversations, many=True).data

    stats = {
        'total_conversations': total_conversations,
        'active_conversations': active_conversations,
        'total_messages': total_messages,
        'avg_messages_per_conversation': round(avg_messages, 2),
        'most_used_agents': most_used_agents,
        'recent_activity': recent_activity
    }

    return Response(stats)


from django.views.decorators.csrf import csrf_exempt
from django.http import StreamingHttpResponse

@csrf_exempt
def send_message_stream_view(request):
    """发送消息并返回SSE流式响应"""
    from django.http import StreamingHttpResponse
    import json
    import time

    conversation_id = request.GET.get('conversation_id')
    agent_id = request.GET.get('agent_id')
    content = request.GET.get('content')
    message_type = request.GET.get('type', 'user')
    is_anonymous = request.GET.get('anonymous', 'false').lower() == 'true'
    llm_provider_id = request.GET.get('llm_provider_id')  # 新增：大模型提供商ID

    # 检查必要参数
    if not content:
        return StreamingHttpResponse(
            iter([f"data: {json.dumps({'type': 'error', 'message': '缺少消息内容'})}\n\n"]),
            content_type='text/event-stream'
        )

    # 对于匿名用户，需要agent_id；对于登录用户，需要conversation_id
    if is_anonymous and not agent_id:
        return StreamingHttpResponse(
            iter([f"data: {json.dumps({'type': 'error', 'message': '匿名用户需要提供智能体ID'})}\n\n"]),
            content_type='text/event-stream'
        )
    elif not is_anonymous and not conversation_id:
        return StreamingHttpResponse(
            iter([f"data: {json.dumps({'type': 'error', 'message': '登录用户需要提供对话ID'})}\n\n"]),
            content_type='text/event-stream'
        )

    def event_stream():
        try:
            if is_anonymous:
                # 匿名用户模式：不创建对话记录，直接调用智能体
                from agents.models import Agent
                agent = get_object_or_404(Agent, id=agent_id)

                # 检查智能体是否公开
                if not agent.is_public:
                    yield f"data: {json.dumps({'type': 'error', 'message': '该智能体不是公开的'})}\n\n"
                    return

                conversation = None
                user_message = None

                # 发送用户消息确认（匿名模式不保存消息）
                yield f"data: {json.dumps({'type': 'user_message', 'message': {'id': 'anonymous', 'content': content}})}\n\n"

                # 选择服务：优先使用用户选择的大模型，否则使用智能体默认服务
                if llm_provider_id:
                    # 使用用户选择的大模型
                    from core.models import LLMProvider
                    from core.llm_service import get_llm_service
                    try:
                        llm_provider = LLMProvider.objects.get(id=llm_provider_id, is_enabled=True)
                        service = get_llm_service(llm_provider)
                    except LLMProvider.DoesNotExist:
                        yield f"data: {json.dumps({'type': 'error', 'message': '选择的大模型不存在或已禁用'})}\n\n"
                        return
                else:
                    # 使用智能体默认服务
                    from agents.services import AIServiceFactory
                    service = AIServiceFactory.create_service(agent)

                # 匿名用户没有对话历史
                conversation_history = []

            else:
                # 登录用户模式：正常的对话流程
                conversation = get_object_or_404(Conversation, id=conversation_id)

                # 创建用户消息
                user_message = Message.objects.create(
                    conversation=conversation,
                    type=message_type,
                    content=content,
                    metadata={}
                )

                # 发送用户消息确认
                yield f"data: {json.dumps({'type': 'user_message', 'message': {'id': str(user_message.id), 'content': content}})}\n\n"

                # 选择服务：优先使用用户选择的大模型，否则使用智能体默认服务
                agent = conversation.agent
                if llm_provider_id:
                    # 使用用户选择的大模型
                    from core.models import LLMProvider
                    from core.llm_service import get_llm_service
                    try:
                        llm_provider = LLMProvider.objects.get(id=llm_provider_id, is_enabled=True)
                        service = get_llm_service(llm_provider)
                    except LLMProvider.DoesNotExist:
                        yield f"data: {json.dumps({'type': 'error', 'message': '选择的大模型不存在或已禁用'})}\n\n"
                        return
                else:
                    # 使用智能体默认服务
                    from agents.services import AIServiceFactory
                    service = AIServiceFactory.create_service(conversation.agent)

                # 获取对话历史（登录用户）
                recent_messages = Message.objects.filter(
                    conversation=conversation
                ).order_by('-created_at')[:10]

                conversation_history = []
                for msg in reversed(recent_messages):
                    if msg.type in ['user', 'assistant']:
                        role = 'user' if msg.type == 'user' else 'assistant'
                        conversation_history.append({
                            'role': role,
                            'content': msg.content
                        })

            # 检查服务是否支持流式响应
            if hasattr(service, 'send_message_stream'):
                # 使用真正的流式响应
                stream_generator = service.send_message_stream(content, conversation_history, conversation)

                full_content = ""
                sources = []
                first_chunk = True

                for chunk_data in stream_generator:
                    if chunk_data['type'] == 'status':
                        # 发送助手开始信号（仅第一次时）
                        if first_chunk:
                            yield f"data: {json.dumps({'type': 'assistant_start'})}\n\n"
                            first_chunk = False

                        # 发送状态消息（会被后续内容覆盖）
                        yield f"data: {json.dumps({'type': 'assistant_status', 'content': chunk_data.get('message', chunk_data.get('content', ''))})}\n\n"

                    elif chunk_data['type'] == 'chunk':
                        # 发送助手开始信号（仅第一个chunk时）
                        if first_chunk:
                            yield f"data: {json.dumps({'type': 'assistant_start'})}\n\n"
                            first_chunk = False

                        # 发送增量内容
                        new_chunk = chunk_data['content']
                        if new_chunk:
                            yield f"data: {json.dumps({'type': 'assistant_chunk', 'content': new_chunk})}\n\n"
                            full_content += new_chunk

                    elif chunk_data['type'] == 'complete':
                        # 提取来源信息和完整内容
                        sources = chunk_data.get('sources', [])
                        # 使用complete事件中的完整内容，如果没有则保持当前累积的内容
                        if 'content' in chunk_data and chunk_data['content']:
                            # 确保使用最完整的内容
                            complete_content = chunk_data['content']
                            if len(complete_content) >= len(full_content):
                                full_content = complete_content
                            # 如果complete中的内容比累积的内容短，保持累积的内容
                        break

                    elif chunk_data['type'] == 'error':
                        # 处理错误
                        yield f"data: {json.dumps({'type': 'error', 'message': chunk_data.get('message', '处理失败')})}\n\n"
                        return

                # 创建助手消息记录（仅登录用户）
                if not is_anonymous and conversation:
                    print(f"保存助手消息到数据库: conversation_id={conversation.id}, content_length={len(full_content)}, content_preview={full_content[:100]}...")
                    assistant_message = Message.objects.create(
                        conversation=conversation,
                        type='assistant',
                        content=full_content,
                        metadata={
                            'model': agent.platform,
                            'sources': sources,
                            'session_id': chunk_data.get('session_id', ''),
                            'message_id': chunk_data.get('message_id', '')
                        }
                    )
                    message_id = str(assistant_message.id)
                    print(f"助手消息保存成功: message_id={message_id}")
                else:
                    message_id = 'anonymous'
                    print(f"匿名用户模式，不保存消息到数据库")

                # 发送完成信号，包含来源信息
                yield f"data: {json.dumps({'type': 'assistant_complete', 'sources': sources, 'message_id': message_id})}\n\n"

            else:
                # 回退到普通响应
                result = service.send_message(content, conversation_history, conversation)
                assistant_content = result.get('content', f"抱歉，{agent.name}暂时无法回复。")
                sources = result.get('sources', [])

                # 发送助手开始信号（修复打字指示器问题）
                yield f"data: {json.dumps({'type': 'assistant_start'})}\n\n"

                # 模拟流式输出（将完整回复分块发送）
                words = assistant_content.split()
                chunk_size = 3  # 每次发送3个词

                for i in range(0, len(words), chunk_size):
                    chunk = ' '.join(words[i:i+chunk_size])
                    if i + chunk_size < len(words):
                        chunk += ' '

                    yield f"data: {json.dumps({'type': 'assistant_chunk', 'content': chunk})}\n\n"
                    time.sleep(0.1)  # 模拟打字延迟

                # 创建助手消息记录（仅登录用户）
                if not is_anonymous and conversation:
                    assistant_message = Message.objects.create(
                        conversation=conversation,
                        type='assistant',
                        content=assistant_content,
                        metadata={'model': agent.platform, 'sources': sources}
                    )
                    message_id = str(assistant_message.id)
                else:
                    message_id = 'anonymous'

                # 发送完成信号
                yield f"data: {json.dumps({'type': 'assistant_complete', 'sources': sources, 'message_id': message_id})}\n\n"

            # 更新智能体使用统计
            agent.increment_usage()

        except Exception as e:
            print(f"SSE流处理失败: {str(e)}")
            yield f"data: {json.dumps({'type': 'error', 'message': f'处理失败: {str(e)}'})}\n\n"

    response = StreamingHttpResponse(event_stream(), content_type='text/event-stream')
    response['Cache-Control'] = 'no-cache'
    response['Access-Control-Allow-Origin'] = '*'
    response['Access-Control-Allow-Headers'] = 'Cache-Control'
    return response
