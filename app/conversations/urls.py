from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>outer
from . import views

# API路由
router = DefaultRouter()
router.register(r'', views.ConversationViewSet, basename='conversation')
router.register(r'messages', views.MessageViewSet, basename='message')

urlpatterns = [
    # 对话相关API
    path('send-message/', views.send_message_view, name='send-message'),
    path('send-message-stream/', views.send_message_stream_view, name='send-message-stream'),
    path('stats/', views.conversation_stats_view, name='conversation-stats'),

    # DRF路由
    path('', include(router.urls)),
]
