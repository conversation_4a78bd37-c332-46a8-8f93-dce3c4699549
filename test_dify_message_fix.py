#!/usr/bin/env python3
"""
验证Dify智能体消息丢失修复效果的测试脚本
"""

import requests
import json
import time

def test_dify_message_completeness():
    """测试Dify消息完整性"""
    
    base_url = "http://localhost:8000"
    agent_id = "a1f45674-b4cf-42f6-ad2d-870e64fe60fd"  # dify-a智能体ID
    
    test_cases = [
        {
            "name": "中文问题测试",
            "content": "请详细介绍一下人工智能的发展历史",
            "expected_min_length": 50
        },
        {
            "name": "英文问题测试", 
            "content": "Tell me about the history of artificial intelligence",
            "expected_min_length": 50
        },
        {
            "name": "复杂问题测试",
            "content": "什么是机器学习？它与深度学习有什么区别？请举例说明",
            "expected_min_length": 80
        }
    ]
    
    print("🔧 测试Dify智能体消息完整性修复效果")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试 {i}: {test_case['name']}")
        print(f"问题: {test_case['content']}")
        print("-" * 50)
        
        url = f"{base_url}/api/conversations/send-message-stream/"
        params = {
            'agent_id': agent_id,
            'content': test_case['content'],
            'type': 'user',
            'anonymous': 'true'
        }
        
        try:
            response = requests.get(url, params=params, stream=True, timeout=60)
            
            if response.status_code != 200:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                continue
            
            # 解析SSE流
            full_content = ""
            chunk_count = 0
            start_time = time.time()
            
            for line in response.iter_lines(decode_unicode=True):
                if not line or not line.startswith('data:'):
                    continue
                
                data_str = line[5:].strip()
                if not data_str:
                    continue
                
                try:
                    data = json.loads(data_str)
                    event_type = data.get('type', '')
                    
                    if event_type == 'assistant_chunk':
                        chunk_content = data.get('content', '')
                        full_content += chunk_content
                        chunk_count += 1
                        # 显示前几个块的内容
                        if chunk_count <= 5:
                            print(f"  📦 块 {chunk_count}: '{chunk_content}'")
                        elif chunk_count == 6:
                            print(f"  📦 ... (更多块)")
                    
                    elif event_type == 'assistant_complete':
                        sources = data.get('sources', [])
                        message_id = data.get('message_id', '')
                        print(f"✅ 消息完成: message_id={message_id}, sources={len(sources)}")
                        break
                    
                    elif event_type == 'error':
                        print(f"❌ 错误: {data.get('message', '未知错误')}")
                        break
                
                except json.JSONDecodeError:
                    continue
            
            # 统计结果
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n📊 测试结果:")
            print(f"   - 总块数: {chunk_count}")
            print(f"   - 完整内容长度: {len(full_content)}")
            print(f"   - 响应时间: {duration:.2f}秒")
            print(f"   - 内容预览: {full_content[:150]}{'...' if len(full_content) > 150 else ''}")
            
            # 验证结果
            if len(full_content) >= test_case['expected_min_length']:
                print(f"✅ 测试通过: 内容长度 {len(full_content)} >= {test_case['expected_min_length']}")
                print(f"🎉 消息完整性良好，无丢失现象")
            else:
                print(f"❌ 测试失败: 内容长度 {len(full_content)} < {test_case['expected_min_length']}")
                print(f"⚠️  可能仍存在消息丢失问题")
            
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
        
        print("-" * 50)
        time.sleep(3)  # 避免请求过于频繁
    
    print("\n" + "=" * 60)
    print("🎯 测试完成! 如果所有测试都通过，说明消息丢失问题已修复")

if __name__ == "__main__":
    test_dify_message_completeness()
