#!/usr/bin/env python3
"""
测试Dify智能体消息丢失修复效果的脚本
"""

import requests
import json
import time

def test_dify_stream_response():
    """测试Dify流式响应"""
    
    # 测试参数
    base_url = "http://localhost:8000"
    agent_id = "a1f45674-b4cf-42f6-ad2d-870e64fe60fd"  # dify-a智能体ID
    
    test_cases = [
        {
            "name": "简单问候",
            "content": "Hello, how are you?",
            "expected_min_length": 10
        },
        {
            "name": "请求长回复",
            "content": "Please tell me a detailed story about a brave knight",
            "expected_min_length": 50
        },
        {
            "name": "技术问题",
            "content": "What is artificial intelligence and how does it work?",
            "expected_min_length": 30
        }
    ]
    
    print("开始测试Dify智能体流式响应修复效果...")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n测试 {i}: {test_case['name']}")
        print(f"问题: {test_case['content']}")
        print("-" * 40)
        
        # 构建请求URL
        url = f"{base_url}/api/conversations/send-message-stream/"
        params = {
            'agent_id': agent_id,
            'content': test_case['content'],
            'type': 'user',
            'anonymous': 'true'
        }
        
        try:
            # 发送流式请求
            response = requests.get(url, params=params, stream=True, timeout=60)
            
            if response.status_code != 200:
                print(f"❌ 请求失败: HTTP {response.status_code}")
                continue
            
            # 解析SSE流
            full_content = ""
            chunk_count = 0
            start_time = time.time()
            
            for line in response.iter_lines(decode_unicode=True):
                if not line or not line.startswith('data:'):
                    continue
                
                data_str = line[5:].strip()
                if not data_str:
                    continue
                
                try:
                    data = json.loads(data_str)
                    event_type = data.get('type', '')
                    
                    if event_type == 'assistant_chunk':
                        chunk_content = data.get('content', '')
                        full_content += chunk_content
                        chunk_count += 1
                        print(f"📝 收到块 {chunk_count}: {chunk_content[:50]}{'...' if len(chunk_content) > 50 else ''}")
                    
                    elif event_type == 'assistant_complete':
                        sources = data.get('sources', [])
                        message_id = data.get('message_id', '')
                        print(f"✅ 消息完成: message_id={message_id}, sources={len(sources)}")
                        break
                    
                    elif event_type == 'error':
                        print(f"❌ 错误: {data.get('message', '未知错误')}")
                        break
                
                except json.JSONDecodeError as e:
                    print(f"⚠️  JSON解析错误: {e}")
                    continue
            
            # 统计结果
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"\n📊 测试结果:")
            print(f"   - 总块数: {chunk_count}")
            print(f"   - 完整内容长度: {len(full_content)}")
            print(f"   - 响应时间: {duration:.2f}秒")
            print(f"   - 内容预览: {full_content[:100]}{'...' if len(full_content) > 100 else ''}")
            
            # 验证结果
            if len(full_content) >= test_case['expected_min_length']:
                print(f"✅ 测试通过: 内容长度 {len(full_content)} >= {test_case['expected_min_length']}")
            else:
                print(f"❌ 测试失败: 内容长度 {len(full_content)} < {test_case['expected_min_length']}")
            
        except Exception as e:
            print(f"❌ 测试异常: {str(e)}")
        
        print("-" * 40)
        time.sleep(2)  # 避免请求过于频繁
    
    print("\n" + "=" * 60)
    print("测试完成!")

if __name__ == "__main__":
    test_dify_stream_response()
